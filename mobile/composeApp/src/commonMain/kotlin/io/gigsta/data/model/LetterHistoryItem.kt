package io.gigsta.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LetterHistoryItem(
    @SerialName("id")
    val id: String,
    @SerialName("plain_text")
    val plainText: String,
    @SerialName("design_html")
    val designHtml: String? = null,
    @SerialName("template_id")
    val templateId: String,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("status")
    val status: String? = null
)

@Serializable
data class LetterTemplate(
    val id: String,
    val name: String
)
