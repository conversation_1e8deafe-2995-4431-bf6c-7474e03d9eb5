package io.gigsta.domain.model

data class HistoryItem(
    val id: String,
    val title: String,
    val subtitle: String,
    val progress: Float,
    val date: String,
    val type: HistoryItemType,
    val templateName: String? = null,
    val content: String? = null,
    val htmlContent: String? = null,
    val status: String? = null
)

enum class HistoryItemType {
    CV_BUILDER,
    APPLICATION_LETTER,
    EMAIL_APPLICATION,
    JOB_MATCH
}
